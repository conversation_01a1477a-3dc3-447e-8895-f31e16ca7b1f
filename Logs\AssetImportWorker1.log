Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 15607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\00ruanjian\00unity\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
-logFile
Logs/AssetImportWorker1.log
-srvPort
52298
Successfully changed project path to: D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [9396]  Target information:

Player connection [9396]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4256026182 [EditorId] 4256026182 [Version] 1048832 [Id] WindowsEditor(7,ANI9) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [9396] Host joined multi-casting on [***********:54997]...
Player connection [9396] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 37.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Laptop GPU (ID=0x28a0)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56596
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006832 seconds.
- Loaded All Assemblies, in  0.661 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1781 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.088 seconds
Domain Reload Profiling: 2747ms
	BeginReloadAssembly (320ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (199ms)
		LoadAssemblies (318ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (196ms)
			TypeCache.Refresh (195ms)
				TypeCache.ScanAssembly (181ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2089ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2045ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1879ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (116ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.994 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.142 seconds
Domain Reload Profiling: 2123ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (698ms)
		LoadAssemblies (579ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (202ms)
				TypeCache.ScanAssembly (173ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1143ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (935ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (673ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 28.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4061 Unused Serialized files (Serialized files now loaded: 0)
Unloading 25 unused Assets / (342.3 KB). Loaded Objects now: 4537.
Memory consumption went from 156.0 MB to 155.6 MB.
Total: 4.506400 ms (FindLiveObjects: 0.374100 ms CreateObjectMapping: 0.346100 ms MarkObjects: 3.600100 ms  DeleteObjects: 0.184600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 43315.766583 seconds.
  path: Assets/11.unity
  artifactKey: Guid(43d11ee6a86347a4e9ba27f19b3b4bb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/11.unity using Guid(43d11ee6a86347a4e9ba27f19b3b4bb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '0ecc98514ce12e6448cc4a31b410b28f') in 0.005183 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.200 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.036 seconds
Domain Reload Profiling: 5233ms
	BeginReloadAssembly (1227ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (866ms)
		LoadAssemblies (966ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (3037ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (734ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (479ms)
			ProcessInitializeOnLoadMethodAttributes (80ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 24.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4039 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (313.9 KB). Loaded Objects now: 4543.
Memory consumption went from 150.8 MB to 150.5 MB.
Total: 38.200100 ms (FindLiveObjects: 0.380500 ms CreateObjectMapping: 0.300700 ms MarkObjects: 37.220900 ms  DeleteObjects: 0.295400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 84fb942ecc3e98743680fb42a007ff71 -> 46454617a3dd37957ea92c1c7af2698d
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d799e7fbc1eb4c0bcc7ff9a56c62afc8 -> 
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.891 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.773 seconds
Domain Reload Profiling: 3662ms
	BeginReloadAssembly (1485ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (315ms)
		LoadAssemblies (386ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (22ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (1774ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (581ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (385ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (14ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 15.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4044 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (313.8 KB). Loaded Objects now: 4551.
Memory consumption went from 151.1 MB to 150.8 MB.
Total: 6.941400 ms (FindLiveObjects: 0.704700 ms CreateObjectMapping: 0.473100 ms MarkObjects: 5.591600 ms  DeleteObjects: 0.170000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: ffaebd46e6fc418a521959ab0aa4f1b4 -> 46454617a3dd37957ea92c1c7af2698d
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: c6b8a2dddd8c1a2e9de67d2a56c8b2d5 -> 
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.108 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.189 seconds
Domain Reload Profiling: 2295ms
	BeginReloadAssembly (748ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (279ms)
		LoadAssemblies (333ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (1190ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (569ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 14.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4038 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (313.7 KB). Loaded Objects now: 4555.
Memory consumption went from 151.0 MB to 150.7 MB.
Total: 7.201500 ms (FindLiveObjects: 0.664800 ms CreateObjectMapping: 0.237500 ms MarkObjects: 5.891400 ms  DeleteObjects: 0.405400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.400 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.785 seconds
Domain Reload Profiling: 3179ms
	BeginReloadAssembly (815ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (483ms)
		LoadAssemblies (438ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (154ms)
			TypeCache.Refresh (111ms)
				TypeCache.ScanAssembly (96ms)
			ScanForSourceGeneratedMonoScriptInfo (36ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (985ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (750ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 19.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4003 Unused Serialized files (Serialized files now loaded: 0)
Unloading 15 unused Assets / (312.8 KB). Loaded Objects now: 4558.
Memory consumption went from 150.4 MB to 150.1 MB.
Total: 7.283900 ms (FindLiveObjects: 0.623500 ms CreateObjectMapping: 0.300200 ms MarkObjects: 6.203100 ms  DeleteObjects: 0.155100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
  custom:scripting/precompiled-assembly-types:websocket-sharp:  -> 0723036bceb1f91a35fc5688554b7745
  custom:scripting/precompiled-assembly-types:McpUnity.Editor:  -> 46fd8d103e13eff83f598ba2f3056ae4
  custom:scripting/precompiled-assembly-types:Unity.EditorCoroutines.Editor:  -> 939c1390cec4719064bc8982808f80e1
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.938 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Port 6400 is already in use. Ensure no other instances are running or change the port.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at Library/PackageCache/com.justinpbarnett.unity-mcp@cbe9b3a2f0/Editor/UnityMcpBridge.cs:89)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at Library/PackageCache/com.justinpbarnett.unity-mcp@cbe9b3a2f0/Editor/UnityMcpBridge.cs:53)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Library/PackageCache/com.justinpbarnett.unity-mcp@cbe9b3a2f0/Editor/UnityMcpBridge.cs Line: 89)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.604 seconds
Domain Reload Profiling: 3539ms
	BeginReloadAssembly (572ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (287ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (2605ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1833ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (1641ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 13.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4027 Unused Serialized files (Serialized files now loaded: 0)
Unloading 15 unused Assets / (312.9 KB). Loaded Objects now: 4585.
Memory consumption went from 150.8 MB to 150.5 MB.
Total: 4.371400 ms (FindLiveObjects: 0.393200 ms CreateObjectMapping: 0.322200 ms MarkObjects: 3.536600 ms  DeleteObjects: 0.118100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/ManageScene.cs: d72592d001754cefc460a063667bc854 -> 
  custom:scripting/monoscript/fileName/ManualConfigEditorWindow.cs: 1441e9e293d42616f93c247e78404be7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/ManageGameObject.cs: ef327751450e75850c42b60a84181d3b -> 
  custom:scripting/monoscript/fileName/Response.cs: a34363d5cf9c87bf1635299b627c5032 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/MCPConfigServers.cs: b8506e93ee93f495cd65795c6652ef66 -> 
  custom:scripting/monoscript/fileName/McpClients.cs: de86770cae4eb69e56c71249b28781f1 -> 
  custom:scripting/monoscript/fileName/Vector3Helper.cs: 9ae95138d998f05a338aadc6e1c246e5 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/ServerConfig.cs: 76311a4c74083f1e766d547cdee610f1 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/UnityMcpEditorWindow.cs: ce37b6adee066676b2eeeaeaa63879c1 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/McpClient.cs: 44a9b141379da46ca54a18b1390eef6b -> 
  custom:scripting/monoscript/fileName/ManageScript.cs: 4df422e81d47e0cccae6a392dfbc9e4b -> 
  custom:scripting/monoscript/fileName/CommandRegistry.cs: ce46f8dc004d1c304d730aa12082cca9 -> 
  custom:scripting/monoscript/fileName/ManageEditor.cs: fcbfee708d05841abda1186f2454282b -> 
  custom:scripting/monoscript/fileName/MCPConfigServer.cs: d74be1ade6c1871e9eea1c81e407b270 -> 
  custom:scripting/monoscript/fileName/DefaultServerConfig.cs: 58d03a41a11a0d3b8a4bc8c7e8424e24 -> 
  custom:scripting/monoscript/fileName/ExecuteMenuItem.cs: c03e7443e3fd8e8bbc194a7c2f756342 -> 
  custom:scripting/monoscript/fileName/ReadConsole.cs: a935bfb395c17926f795f71ebc6667fa -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/ServerInstaller.cs: cbfdf46c9fb9f256347395d32828a9d0 -> 
  custom:scripting/monoscript/fileName/ManageAsset.cs: 37c760ebfcf2367f89c04374ac938c13 -> 
  custom:scripting/monoscript/fileName/Command.cs: 2184415f8eaef09c8189fbf6df5c0b6e -> 
  custom:scripting/monoscript/fileName/UnityMcpBridge.cs: 4a3723716ef216c9d347c5d39b3041f8 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 0ec209531dec0342e256a6aeb277288b -> 46454617a3dd37957ea92c1c7af2698d
  custom:scripting/monoscript/fileName/McpConfig.cs: 680aa3dd3d8a652a19bfc9a11bbce1f1 -> 
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
  custom:scripting/precompiled-assembly-types:websocket-sharp:  -> 0723036bceb1f91a35fc5688554b7745
  custom:scripting/precompiled-assembly-types:McpUnity.Editor:  -> 46fd8d103e13eff83f598ba2f3056ae4
  custom:scripting/precompiled-assembly-types:Unity.EditorCoroutines.Editor:  -> 939c1390cec4719064bc8982808f80e1
========================================================================
Received Import Request.
  Time since last request: 1744.075005 seconds.
  path: Assets/test/Materials/木头材质.mat
  artifactKey: Guid(dab497f8cffdbf24e9969b8348f024cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Materials/木头材质.mat using Guid(dab497f8cffdbf24e9969b8348f024cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e530443254a9d4a05c007d292829987e') in 0.155699 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/test/Materials/龙鳞材质.mat
  artifactKey: Guid(2511500b04e525e4398873991a52e235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Materials/龙鳞材质.mat using Guid(2511500b04e525e4398873991a52e235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '5b4ffb65e55224174bd43cc0349529a8') in 0.009166 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000116 seconds.
  path: Assets/test/Materials/翅膀材质.mat
  artifactKey: Guid(b1d9d2312773268418886cea0b54a387) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Materials/翅膀材质.mat using Guid(b1d9d2312773268418886cea0b54a387) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'bb0965f5507c41046c3094524eaf5078') in 0.006526 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1

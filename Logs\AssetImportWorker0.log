Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 15607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\00ruanjian\00unity\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
-logFile
Logs/AssetImportWorker0.log
-srvPort
52298
Successfully changed project path to: D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25296]  Target information:

Player connection [25296]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2989048905 [EditorId] 2989048905 [Version] 1048832 [Id] WindowsEditor(7,ANI9) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25296] Host joined multi-casting on [***********:54997]...
Player connection [25296] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 37.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Laptop GPU (ID=0x28a0)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56532
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.011343 seconds.
- Loaded All Assemblies, in  0.658 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1791 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.095 seconds
Domain Reload Profiling: 2752ms
	BeginReloadAssembly (300ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (239ms)
		LoadAssemblies (298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (235ms)
				TypeCache.ScanAssembly (216ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2095ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2050ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1887ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (116ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.997 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.141 seconds
Domain Reload Profiling: 2125ms
	BeginReloadAssembly (191ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (700ms)
		LoadAssemblies (581ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (244ms)
			TypeCache.Refresh (204ms)
				TypeCache.ScanAssembly (177ms)
			ScanForSourceGeneratedMonoScriptInfo (35ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1141ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (924ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (660ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 32.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4061 Unused Serialized files (Serialized files now loaded: 0)
Unloading 25 unused Assets / (342.3 KB). Loaded Objects now: 4537.
Memory consumption went from 156.0 MB to 155.6 MB.
Total: 4.400200 ms (FindLiveObjects: 0.342600 ms CreateObjectMapping: 0.315400 ms MarkObjects: 3.569300 ms  DeleteObjects: 0.171600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 43318.540808 seconds.
  path: Assets/test.unity
  artifactKey: Guid(23e06bd5a5ef54b4dbb9742aa7832201) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test.unity using Guid(23e06bd5a5ef54b4dbb9742aa7832201) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '5cfdf1432311adcfb02be2c4d8774303') in 0.002925 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 167.111317 seconds.
  path: Assets/test
  artifactKey: Guid(9160639940e86a240b832640abe2215d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test using Guid(9160639940e86a240b832640abe2215d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '5d8de919fdaf809d40860f7f33018286') in 0.000989 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.379 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.929 seconds
Domain Reload Profiling: 5306ms
	BeginReloadAssembly (1294ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (977ms)
		LoadAssemblies (1040ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (70ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (26ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (742ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 24.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4039 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (313.8 KB). Loaded Objects now: 4543.
Memory consumption went from 150.8 MB to 150.5 MB.
Total: 25.883000 ms (FindLiveObjects: 0.470100 ms CreateObjectMapping: 0.357800 ms MarkObjects: 24.741700 ms  DeleteObjects: 0.310300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 84fb942ecc3e98743680fb42a007ff71 -> 46454617a3dd37957ea92c1c7af2698d
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d799e7fbc1eb4c0bcc7ff9a56c62afc8 -> 
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.882 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.764 seconds
Domain Reload Profiling: 3642ms
	BeginReloadAssembly (1480ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (310ms)
		LoadAssemblies (382ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (23ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (1764ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (555ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 15.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4044 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (313.7 KB). Loaded Objects now: 4551.
Memory consumption went from 151.1 MB to 150.8 MB.
Total: 7.414500 ms (FindLiveObjects: 0.668800 ms CreateObjectMapping: 0.323300 ms MarkObjects: 6.282400 ms  DeleteObjects: 0.138800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: ffaebd46e6fc418a521959ab0aa4f1b4 -> 46454617a3dd37957ea92c1c7af2698d
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: c6b8a2dddd8c1a2e9de67d2a56c8b2d5 -> 
========================================================================
Received Import Request.
  Time since last request: 438.311120 seconds.
  path: Assets/test/Editor
  artifactKey: Guid(ebb506651de2d064d8927514bf420f23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Editor using Guid(ebb506651de2d064d8927514bf420f23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'dfc5166e8f283810deb656a80817e69a') in 0.009066 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.157255 seconds.
  path: Assets/test/Editor/DirectGenerate.cs
  artifactKey: Guid(f9e9f982b48bdc44697957f6bcedcbef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Editor/DirectGenerate.cs using Guid(f9e9f982b48bdc44697957f6bcedcbef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'b013cca01e48d490ec805ca4665f9716') in 0.000569 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.957784 seconds.
  path: Assets/test/Editor/DungeonSceneGeneratorEditor.cs
  artifactKey: Guid(c042cba57ce94ea4e9b9086b264cc2c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Editor/DungeonSceneGeneratorEditor.cs using Guid(c042cba57ce94ea4e9b9086b264cc2c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '6c2b8ea4b59efb5fb8848478fcd0137c') in 0.000679 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.254178 seconds.
  path: Assets/test/Editor/GenerateDungeonNow.cs
  artifactKey: Guid(f1a2a2137abe76245a45bb1da8306184) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/Editor/GenerateDungeonNow.cs using Guid(f1a2a2137abe76245a45bb1da8306184) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'b2a31acd35659866c21dd34987441172') in 0.000612 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.882537 seconds.
  path: Assets/test/CallGenerateMethod.cs
  artifactKey: Guid(d55218ff1e453664daf835fa3829da72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/CallGenerateMethod.cs using Guid(d55218ff1e453664daf835fa3829da72) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e2c1cbced8a16825313a7f8c4ef6cfd1') in 0.000575 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.032830 seconds.
  path: Assets/test/DungeonSceneGenerator.cs
  artifactKey: Guid(f5ab2153f2aa1d64f9bffdc5ed057cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/DungeonSceneGenerator.cs using Guid(f5ab2153f2aa1d64f9bffdc5ed057cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '1e96150485250cbe4179c3b2b9d37325') in 0.000731 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.415689 seconds.
  path: Assets/test/SimpleGenerate.cs
  artifactKey: Guid(3a35d8af20799b244ae93c9c053fff48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/test/SimpleGenerate.cs using Guid(3a35d8af20799b244ae93c9c053fff48) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '3323670d206924ead2904138d72521c0') in 0.000539 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 23.824316 seconds.
  path: Packages/com.gamelovers.mcp-unity/package.json
  artifactKey: Guid(3d2f22329e359c546ba72a6b86dc703b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.gamelovers.mcp-unity/package.json using Guid(3d2f22329e359c546ba72a6b86dc703b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: '6c0e5f459220dae7bc2e5016d572f181') in 0.018895 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 2.779904 seconds.
  path: Packages/com.gamelovers.mcp-unity/Editor/McpUnity.Editor.asmdef
  artifactKey: Guid(7f4025ba8338b424b8027ef87ad77768) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.gamelovers.mcp-unity/Editor/McpUnity.Editor.asmdef using Guid(7f4025ba8338b424b8027ef87ad77768) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
 -> (artifact id: 'e3804776db713ebaa27c5f90b6d75b42') in 0.000771 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.116 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Error updating workspace file: Sharing violation on path D:\BaiduNetdiskDownload\UnityProject\AutoColliderSetUp-master\AutoColliderSetUp-master.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:71)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 71)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.187 seconds
Domain Reload Profiling: 2301ms
	BeginReloadAssembly (767ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (270ms)
		LoadAssemblies (325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (1188ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (391ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4037 Unused Serialized files (Serialized files now loaded: 0)
Unloading 16 unused Assets / (313.8 KB). Loaded Objects now: 4556.
Memory consumption went from 150.8 MB to 150.5 MB.
Total: 6.332400 ms (FindLiveObjects: 0.588200 ms CreateObjectMapping: 0.213600 ms MarkObjects: 5.355800 ms  DeleteObjects: 0.173000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.398 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.802 seconds
Domain Reload Profiling: 3197ms
	BeginReloadAssembly (803ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (503ms)
		LoadAssemblies (434ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (129ms)
				TypeCache.ScanAssembly (101ms)
			ScanForSourceGeneratedMonoScriptInfo (40ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1803ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1001ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (762ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 14.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4003 Unused Serialized files (Serialized files now loaded: 0)
Unloading 15 unused Assets / (313.2 KB). Loaded Objects now: 4559.
Memory consumption went from 150.4 MB to 150.1 MB.
Total: 6.923600 ms (FindLiveObjects: 0.624800 ms CreateObjectMapping: 0.224600 ms MarkObjects: 5.850800 ms  DeleteObjects: 0.221900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
  custom:scripting/precompiled-assembly-types:websocket-sharp:  -> 0723036bceb1f91a35fc5688554b7745
  custom:scripting/precompiled-assembly-types:McpUnity.Editor:  -> 46fd8d103e13eff83f598ba2f3056ae4
  custom:scripting/precompiled-assembly-types:Unity.EditorCoroutines.Editor:  -> 939c1390cec4719064bc8982808f80e1
========================================================================
Received Prepare
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.956 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Port 6400 is already in use. Ensure no other instances are running or change the port.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at Library/PackageCache/com.justinpbarnett.unity-mcp@cbe9b3a2f0/Editor/UnityMcpBridge.cs:89)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at Library/PackageCache/com.justinpbarnett.unity-mcp@cbe9b3a2f0/Editor/UnityMcpBridge.cs:53)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: Library/PackageCache/com.justinpbarnett.unity-mcp@cbe9b3a2f0/Editor/UnityMcpBridge.cs Line: 89)

[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.812 seconds
Domain Reload Profiling: 3765ms
	BeginReloadAssembly (580ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (296ms)
		LoadAssemblies (353ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (3ms)
	FinalizeReload (2812ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2045ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (97ms)
			ProcessInitializeOnLoadAttributes (1798ms)
			ProcessInitializeOnLoadMethodAttributes (119ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 13.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4027 Unused Serialized files (Serialized files now loaded: 0)
Unloading 15 unused Assets / (313.4 KB). Loaded Objects now: 4586.
Memory consumption went from 150.8 MB to 150.5 MB.
Total: 4.983200 ms (FindLiveObjects: 0.344100 ms CreateObjectMapping: 0.250400 ms MarkObjects: 4.289200 ms  DeleteObjects: 0.098500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/DungeonSceneGeneratorEditor.cs: 40658e31d1607e585c782a92302ca58f -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/ManageScene.cs: d72592d001754cefc460a063667bc854 -> 
  custom:scripting/monoscript/fileName/ManualConfigEditorWindow.cs: 1441e9e293d42616f93c247e78404be7 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/ManageGameObject.cs: ef327751450e75850c42b60a84181d3b -> 
  custom:scripting/monoscript/fileName/Response.cs: a34363d5cf9c87bf1635299b627c5032 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/MCPConfigServers.cs: b8506e93ee93f495cd65795c6652ef66 -> 
  custom:scripting/monoscript/fileName/McpClients.cs: de86770cae4eb69e56c71249b28781f1 -> 
  custom:scripting/monoscript/fileName/Vector3Helper.cs: 9ae95138d998f05a338aadc6e1c246e5 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/CallGenerateMethod.cs: 7ee8299268a8e029ed6bb17fcf10f2bb -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/DungeonSceneGenerator.cs: 2b0db77e9b73c330528251776c66f369 -> 
  custom:scripting/monoscript/fileName/ServerConfig.cs: 76311a4c74083f1e766d547cdee610f1 -> 
  custom:scripting/monoscript/fileName/GenerateDungeonNow.cs: 8440d78bc3690351661d45862ddfad22 -> 
  custom:scripting/monoscript/fileName/SimpleGenerate.cs: c843e9cdaae1e2e41ea332d1aa347951 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/UnityMcpEditorWindow.cs: ce37b6adee066676b2eeeaeaa63879c1 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/McpClient.cs: 44a9b141379da46ca54a18b1390eef6b -> 
  custom:scripting/monoscript/fileName/ManageScript.cs: 4df422e81d47e0cccae6a392dfbc9e4b -> 
  custom:scripting/monoscript/fileName/CommandRegistry.cs: ce46f8dc004d1c304d730aa12082cca9 -> 
  custom:scripting/monoscript/fileName/ManageEditor.cs: fcbfee708d05841abda1186f2454282b -> 
  custom:scripting/monoscript/fileName/MCPConfigServer.cs: d74be1ade6c1871e9eea1c81e407b270 -> 
  custom:scripting/monoscript/fileName/DefaultServerConfig.cs: 58d03a41a11a0d3b8a4bc8c7e8424e24 -> 
  custom:scripting/monoscript/fileName/ExecuteMenuItem.cs: c03e7443e3fd8e8bbc194a7c2f756342 -> 
  custom:scripting/monoscript/fileName/ReadConsole.cs: a935bfb395c17926f795f71ebc6667fa -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/ServerInstaller.cs: cbfdf46c9fb9f256347395d32828a9d0 -> 
  custom:scripting/monoscript/fileName/ManageAsset.cs: 37c760ebfcf2367f89c04374ac938c13 -> 
  custom:scripting/monoscript/fileName/Command.cs: 2184415f8eaef09c8189fbf6df5c0b6e -> 
  custom:scripting/monoscript/fileName/UnityMcpBridge.cs: 4a3723716ef216c9d347c5d39b3041f8 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 0ec209531dec0342e256a6aeb277288b -> 46454617a3dd37957ea92c1c7af2698d
  custom:scripting/monoscript/fileName/McpConfig.cs: 680aa3dd3d8a652a19bfc9a11bbce1f1 -> 
  custom:scripting/monoscript/fileName/DirectGenerate.cs: c4215f2bc9197a8334df49a4ff3a3d84 -> 
  custom:scripting/precompiled-assembly-types:websocket-sharp:  -> 0723036bceb1f91a35fc5688554b7745
  custom:scripting/precompiled-assembly-types:McpUnity.Editor:  -> 46fd8d103e13eff83f598ba2f3056ae4
  custom:scripting/precompiled-assembly-types:Unity.EditorCoroutines.Editor:  -> 939c1390cec4719064bc8982808f80e1

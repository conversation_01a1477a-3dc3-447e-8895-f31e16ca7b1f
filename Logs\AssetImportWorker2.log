Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 15607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\00ruanjian\00unity\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
-logFile
Logs/AssetImportWorker2.log
-srvPort
52298
Successfully changed project path to: D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24292]  Target information:

Player connection [24292]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3483128457 [EditorId] 3483128457 [Version] 1048832 [Id] WindowsEditor(7,ANI9) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24292] Host joined multi-casting on [***********:54997]...
Player connection [24292] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 16 workers.
Refreshing native plugins compatible for Editor in 15.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/BaiduNetdiskDownload/UnityProject/AutoColliderSetUp-master/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 Laptop GPU (ID=0x28a0)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.7628
Initialize mono
Mono path[0] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56480
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/00ruanjian/00unity/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006172 seconds.
- Loaded All Assemblies, in  0.302 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 390 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.652 seconds
Domain Reload Profiling: 953ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (126ms)
		LoadAssemblies (88ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (124ms)
			TypeCache.Refresh (122ms)
				TypeCache.ScanAssembly (111ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (612ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (457ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (105ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Assembly reference Packages/org.khronos.unitygltf/Runtime/Scripts/Interactivity/VisualScripting/Units/Unity.VisualScripting.asmref has no target assembly definition
Assembly reference Packages/org.khronos.unitygltf/Editor/Scripts/ShaderGraph/InternalURP/UnityGLTF.URP.Internal.asmref has no target assembly definition
Begin MonoManager ReloadAssembly

﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(Configuration)\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2022_3_47;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2022.3.47f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode-insiders\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\UnityMcpBridge.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\Command.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Helpers\ServerInstaller.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Windows\UnityMcpEditorWindow.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ManageScript.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ManageScene.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Data\DefaultServerConfig.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\McpStatus.cs" />
    <Compile Include="Assets\Editor\RemoveMissingScriptsFromPrefabs.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\CommandRegistry.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Windows\ManualConfigEditorWindow.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ManageAsset.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ExecuteMenuItem.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ManageEditor.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\McpClient.cs" />
    <Compile Include="Assets\Editor\PhysicsDataConverter.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ManageGameObject.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\MCPConfigServers.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\McpConfig.cs" />
    <Compile Include="Assets\Editor\UnityToUE5PhysicsExporter.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Data\McpClients.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\ServerConfig.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Helpers\Response.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Helpers\Vector3Helper.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\MCPConfigServer.cs" />
    <Compile Include="Assets\Editor\HierarchyComponentRemover.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Tools\ReadConsole.cs" />
    <Compile Include="Library\PackageCache\com.justinpbarnett.unity-mcp@cbe9b3a2f0\Editor\Models\McpTypes.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\differences.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm-help.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\semantic.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\basecommands.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\binmergetool.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\commontypes.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\guihelp.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\workspaceserver.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\i3.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\commontypes.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\xdiff.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\i3.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\differences.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\common.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\images.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\basecommands.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\xdiff.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\mergetool.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\i3.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\guihelp.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\basecommands.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\plastic-gui.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm-help.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\mergetool.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\unityplastic.dll" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Unity.Plastic.Antlr3.Runtime.dll" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\guihelp.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\configurehelper.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\binmergetool.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\basecommands.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\images.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\guihelp.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\mergetool.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\clientcommon.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\xdiff.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\i3.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Unity.Plastic.Newtonsoft.Json.dll" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\workspaceserver.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\plastic-gui.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\semantic.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\plastic-gui.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\configurehelper.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\binmergetool.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\binmergetool.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\plastic-gui.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\log4netPlastic.dll" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\lz4x64Plastic.dll" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\workspaceserver.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\guihelp.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\binmergetool.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\images.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\binmergetool.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\clientcommon.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\commontypes.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\semantic.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\basecommands.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\differences.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm-help.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm-help.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\images.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\i3.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\commontypes.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\configurehelper.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\workspaceserver.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\mergetool.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\clientcommon.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\FileSystemWatcherLicense.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\commontypes.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\clientcommon.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\basecommands.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\xdiff.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\differences.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\images.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\mergetool.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\differences.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\configurehelper.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\semantic.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\mergetool.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\workspaceserver.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\i3.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\images.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\semantic.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\plastic-gui.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\xdiff.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\common.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\clientcommon.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\guihelp.es.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\configurehelper.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\clientcommon.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\semantic.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\configurehelper.ko.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm-help.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\workspaceserver.en.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm-help.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\zlib64Plastic.dll" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\plastic-gui.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\commontypes.ja.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\xdiff.zh-Hant.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\cm.zh-Hans.txt" />
    <None Include="Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Localization\differences.en.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Analytics.Tracker">
      <HintPath>Library\PackageCache\com.unity.analytics@3.8.1\Unity.Analytics.Tracker.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Analytics.Editor">
      <HintPath>Library\PackageCache\com.unity.analytics@3.8.1\Unity.Analytics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@2.5.1\Lib\Editor\PlasticSCM\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@3.2.1\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Analytics.StandardEvents">
      <HintPath>Library\PackageCache\com.unity.analytics@3.8.1\AnalyticsStandardEvents\Unity.Analytics.StandardEvents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\00ruanjian\00unity\2022.3.47f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="UnityEditor.TestRunner.csproj" />
    <ProjectReference Include="UnityEngine.TestRunner.csproj" />
    <ProjectReference Include="Unity.Services.Analytics.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.csproj" />
    <ProjectReference Include="Unity.Analytics.DataPrivacy.csproj" />
    <ProjectReference Include="Unity.Services.Core.csproj" />
    <ProjectReference Include="UnityGLTF.Interactivity.VisualScriptingInstall.csproj" />
    <ProjectReference Include="Unity.Rider.Editor.csproj" />
    <ProjectReference Include="Unity.2D.Sprite.Editor.csproj" />
    <ProjectReference Include="Unity.Formats.Fbx.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj" />
    <ProjectReference Include="Unity.Mathematics.Editor.csproj" />
    <ProjectReference Include="UnityGLTF.Interactivity.Runtime.csproj" />
    <ProjectReference Include="Unity.Services.Core.Environments.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.ShaderLibrary.csproj" />
    <ProjectReference Include="Unity.Formats.Fbx.Editor.csproj" />
    <ProjectReference Include="UnityGLTFScripts.csproj" />
    <ProjectReference Include="GLTFSerialization.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.csproj" />
    <ProjectReference Include="Unity.Services.Core.Analytics.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.Editor.ConversionSystem.csproj" />
    <ProjectReference Include="Unity.VSCode.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj" />
    <ProjectReference Include="UnityEngine.XR.LegacyInputHelpers.csproj" />
    <ProjectReference Include="Unity.PlasticSCM.Editor.csproj" />
    <ProjectReference Include="UnityEngine.Advertisements.csproj" />
    <ProjectReference Include="Unity.Timeline.Editor.csproj" />
    <ProjectReference Include="Unity.ShaderGraph.Editor.csproj" />
    <ProjectReference Include="UnityEditor.SpatialTracking.csproj" />
    <ProjectReference Include="Unity.VisualStudio.Editor.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.SpatialTracking.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="Analytics.csproj" />
    <ProjectReference Include="UnityEngine.Advertisements.Editor.csproj" />
    <ProjectReference Include="UnityEditor.XR.LegacyInputHelpers.csproj" />
    <ProjectReference Include="Unity.Timeline.csproj" />
    <ProjectReference Include="Unity.Services.Analytics.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.Updater.csproj" />
    <ProjectReference Include="Autodesk.Fbx.csproj" />
    <ProjectReference Include="Autodesk.Fbx.Editor.csproj" />
    <ProjectReference Include="Unity.Mathematics.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.Editor.csproj" />
    <ProjectReference Include="Unity.Searcher.Editor.csproj" />
    <ProjectReference Include="Unity.AI.Navigation.Editor.csproj" />
    <ProjectReference Include="Unity.2D.Tilemap.Editor.csproj" />
    <ProjectReference Include="UnityGLTFEditor.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
